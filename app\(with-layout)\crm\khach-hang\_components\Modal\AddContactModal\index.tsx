import { useCreateContact } from '@/apis/contact/contact.api';
import FormController from '@/components/common/FormController';
import { honorifics } from '@/constants/sharedData/sharedData';
import useGetOptionsDepartments from '@/hooks/useGetOptionsDepartments';
import useGetOptionsPosition from '@/hooks/useGetOptionsPosition';
import React from 'react';
import { useForm, FormProvider } from 'react-hook-form';
import {
    <PERSON><PERSON>,
    <PERSON>dalHeader,
    ModalBody,
    ModalFooter,
    Button,
    Col,
    Row,
} from 'reactstrap';
import { toast } from 'react-toastify';
import { IContact } from '@/apis/contact/contact.type';

interface AddContactModalProps {
    isOpen: boolean;
    toggle: () => void;
    onSuccess?: () => void;
}

const AddContactModal: React.FC<AddContactModalProps> = ({
    isOpen,
    toggle,
    onSuccess,
}) => {
    const methods = useForm<IContact>({
        defaultValues: {
            honorific: undefined,
            name: '',
            email: '',
            phoneNumber: '',
            departmentId: '',
            positionId: '',
            roleName: '',
        },
    });

    const { mutate: createContact } = useCreateContact({
        onSuccess: () => {
            toast.success('Tạo mới liên hệ thành công');
            methods.reset();
            toggle();
            onSuccess?.();
        },
        onError: (error) => {
            toast.error(error.message);
        },
    });

    const handleSubmit = methods.handleSubmit((data: IContact) => {
        data.honorific = Number(data.honorific);
        createContact(data);
    });

    const { departments } = useGetOptionsDepartments();
    const { positions } = useGetOptionsPosition();

    const formattedDepartments = departments.map((item) => ({
        ...item,
        value: item.value.toString(),
    }));

    const formattedPositions = positions.map((item) => ({
        ...item,
        value: item.value.toString(),
    }));

    return (
        <Modal
            isOpen={isOpen}
            toggle={toggle}
            style={{
                position: 'fixed',
                right: 0,
                top: 0,
                margin: 0,
                maxWidth: '500px',
                width: '100%',
                height: '100vh',
                transform: 'none',
            }}
            contentClassName='h-100 border-0'
            modalClassName='position-fixed end-0 top-0 h-100 m-0'
        >
            <FormProvider {...methods}>
                <ModalHeader toggle={toggle}>
                    <div
                        style={{
                            color: '#45c3b2',
                        }}
                    >
                        Tạo cá nhân
                    </div>
                </ModalHeader>

                <ModalBody
                    style={{
                        flex: 1,
                        overflowY: 'auto',
                        padding: '20px',
                    }}
                >
                    <Row className='g-3'>
                        <Col md='12'>
                            <FormController
                                controlType='select'
                                name='honorific'
                                label='Xưng hô'
                                data={honorifics}
                                placeholder='Chọn cách xưng hô'
                            />
                        </Col>

                        <Col md='12'>
                            <FormController
                                controlType='textInput'
                                name='name'
                                label='Họ và tên'
                                placeholder='Nhập họ và tên cá nhân'
                                required={true}
                            />
                        </Col>

                        <Col md='12'>
                            <FormController
                                controlType='textInput'
                                name='email'
                                label='Email'
                                placeholder='Nhập email cá nhân'
                                required={true}
                            />
                        </Col>

                        <Col md='12'>
                            <FormController
                                controlType='textInput'
                                name='phoneNumber'
                                label='Số điện thoại'
                                placeholder='Nhập số điện thoại cá nhân'
                                required={true}
                            />
                        </Col>
                        <Col md='12'>
                            <FormController
                                controlType='select'
                                name='departmentId'
                                data={formattedDepartments}
                                label='Phòng ban'
                                placeholder='Chọn phòng ban'
                                required={true}
                            />
                        </Col>

                        <Col md='12'>
                            <FormController
                                controlType='select'
                                name='positionId'
                                data={formattedPositions}
                                label='Chức vụ'
                                placeholder='Chọn chức vụ'
                                required={true}
                            />
                        </Col>
                        <Col md='12'>
                            <FormController
                                controlType='textInput'
                                name='roleName'
                                label='Vai trò'
                                placeholder='Nhập vai trò của cá nhân trong khách hàng'
                                required={false}
                            />
                        </Col>
                    </Row>
                </ModalBody>
                <ModalFooter>
                    <Button
                        onClick={toggle}
                        style={{
                            backgroundColor: '#ffffff',
                            color: '#F06548',
                            borderColor: '#F06548',
                        }}
                    >
                        Hủy
                    </Button>
                    <Button
                        onClick={handleSubmit}
                        style={{
                            backgroundColor: '#0AB39C',
                            color: '#ffffff',
                            borderColor: '#0AB39C',
                        }}
                    >
                        Tạo mới
                    </Button>
                </ModalFooter>
            </FormProvider>
        </Modal>
    );
};

export default AddContactModal;
