/* Modal hiển thị bên ph<PERSON>i */
.modal-right-container {
    position: fixed !important;
    right: 0 !important;
    top: 0 !important;
    height: 100vh !important;
    margin: 0 !important;
    max-width: 500px !important;
    width: 100% !important;
    transform: none !important;
}

.modal-right-container .modal-content {
    height: 100vh !important;
    border-radius: 0 !important;
    border: none !important;
    display: flex !important;
    flex-direction: column !important;
}

.modal-right-container .modal-body {
    flex: 1 !important;
    overflow-y: auto !important;
    padding: 20px !important;
}

.modal-right-container .modal-header {
    border-bottom: 1px solid #dee2e6 !important;
    padding: 15px 20px !important;
    flex-shrink: 0 !important;
}

.modal-right-container .modal-footer {
    border-top: 1px solid #dee2e6 !important;
    padding: 15px 20px !important;
    flex-shrink: 0 !important;
}

/* Animation cho modal slide từ phải */
.modal-right.fade .modal-right-container {
    transform: translateX(100%) !important;
    transition: transform 0.3s ease-out !important;
}

.modal-right.show .modal-right-container {
    transform: translateX(0) !important;
}

/* Backdrop */
.modal-right .modal-backdrop {
    background-color: rgba(0, 0, 0, 0.5) !important;
}
